[project]
name = "dts-fb-activities"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
authors = [
    { name = "DocSJ1980", email = "<EMAIL>" }
]
requires-python = ">=3.13"
dependencies = [
    "beautifulsoup4>=4.13.4",
    "fastapi>=0.115.13",
    "openpyxl>=3.1.5",
    "pandas>=2.3.0",
    "pydantic-settings>=2.10.0",
    "python-dotenv>=1.1.1",
    "requests>=2.32.4",
    "uvicorn[standard]>=0.34.3",
]

[project.scripts]
dts-fb-activities = "dts_fb_activities:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
