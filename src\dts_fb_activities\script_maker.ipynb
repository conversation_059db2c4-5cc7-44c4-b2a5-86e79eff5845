from dotenv import load_dotenv
import os
import re
import requests
import json
from bs4 import BeautifulSoup
# Load environment variables
load_dotenv()
import pandas as pd


PITB_USERNAME = os.getenv("PITB_USERNAME")
PITB_PASSWORD = os.getenv("PITB_PASSWORD")
REPORT_TYPE = "indoor"
COOKIE_URL = os.getenv("COOKIE_URL")

def get_cookie_value():
    """
    Retrieve authentication cookie from the cookie service.
    Returns a cookie value for dashboard authentication.
    """
    url = COOKIE_URL

    # Prepare authentication payload
    payload = json.dumps({"username": PITB_USERNAME, "password": PITB_PASSWORD})

    # Set content type for JSON request
    headers = {"Content-Type": "application/json"}

    # Make authentication request
    response = requests.post(url, headers=headers, data=payload)
    response.raise_for_status()  # Raise exception for HTTP errors

    # Parse response and extract cookie
    response_json = response.json()
    print(response_json)
    if response_json.get("success") and "cookies" in response_json:
        return response_json["cookies"][0]["value"]
    else:
        raise ValueError("Invalid response format or unsuccessful request.")


def fetch_page_data(cookie_value, target_date, town_code, uc_code, page_number=1):
    """
    Fetch page data from the dashboard API.
    Returns a JSON object containing page data.
    """
    formatted_date = target_date.strftime("%Y-%m-%d")

    headers = {"Cookie": f"_dengue_new_session={cookie_value}"}
    
    url = f"https://dashboard-tracking.punjab.gov.pk/activities/vector_surveillances/line_list?activity_type=indoor&datefrom={formatted_date}T00%3A00&dateto={formatted_date}T23%3A59&district_id=31&larvae_found=&page={page_number}&tehsil_id={town_code}&uc={uc_code}"

    response = requests.get(url, headers=headers, data={}, verify=False)

    # Debug information
    # print(f"Response URL: {response.url}")
    # print(f"Response status code: {response.status_code}")

    # Check if we got redirected to login page
    if "login" in response.url.lower() or "sign in" in response.text.lower():
        print("Authentication failed - redirected to login page")

    response.raise_for_status()
    return response.content


def read_town_uc_codes(file_path):
  """
  Reads the 'town-uc-codes.xlsx' file and returns:
  - town_data: DataFrame with columns ['town_name', 'town_code'] from the 'town' sheet
  - uc_data: DataFrame with columns ['uc_name', 'town_code', 'uc_code'] from the 'uc' sheet
  """

  # Read the 'town' sheet
  town_data = pd.read_excel(file_path, sheet_name='town', usecols=['town_name', 'town_code'])

  # Read the 'uc' sheet
  uc_data = pd.read_excel(file_path, sheet_name='uc', usecols=['uc_name', 'town_code', 'uc_code'])

  return town_data, uc_data

cookie_value = get_cookie_value()

def get_total_records(page_data):
  # Extract the number after "Total Records:"
  match = re.search(r'Total Records:\s*</b>\s*(\d+)', page_data.decode('utf-8'))
  if match:
    total_records = int(match.group(1))
    print(total_records)
  else:
    print("Total Records not found.")
  return total_records

def get_excel_data(cookie_value, target_date, town_code, uc_code, page_number=1):
    """
    Fetch page data from the dashboard API.
    Returns a JSON object containing page data.
    """
    formatted_date = target_date.strftime("%Y-%m-%d")

    headers = {"Cookie": f"_dengue_new_session={cookie_value}"}
    
    url = f"https://dashboard-tracking.punjab.gov.pk//activities/vector_surveillances/line_list?action=line_list&activity_type=indoor&controller=activities%2Fvector_surveillances&datefrom={formatted_date}T00%3A00&dateto={formatted_date}T23%3A59&district_id=31&format=xls&larvae_found=&page=1&pagination=No&tehsil_id={town_code}&uc={uc_code}"

    response = requests.get(url, headers=headers, data={}, verify=False)

    # Debug information
    # print(f"Response URL: {response.url}")
    # print(f"Response status code: {response.status_code}")

    # Check if we got redirected to login page
    if "login" in response.url.lower() or "sign in" in response.text.lower():
        print("Authentication failed - redirected to login page")

    response.raise_for_status()
    return response.content


def parse_html_table_to_dataframe(html_content):
    """
    Parse HTML table content and extract Activity ID, Latitude, and Longitude columns.
    Returns a pandas DataFrame with only the specified columns.
    """
    # Parse HTML content
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # Find the table
    table = soup.find('table')
    if not table:
        print("No table found in HTML content")
        return pd.DataFrame()
    
    # Extract headers
    headers = []
    header_row = table.find('thead').find('tr')
    for th in header_row.find_all('th'):
        headers.append(th.get_text(strip=True))
    
    # Extract data rows
    rows_data = []
    tbody = table.find('tbody')
    if tbody:
        for row in tbody.find_all('tr'):
            row_data = []
            for td in row.find_all('td'):
                # Get text content, replacing <br/> with spaces if needed
                cell_text = td.get_text(separator=' ', strip=True)
                row_data.append(cell_text)
            rows_data.append(row_data)
    
    # Create DataFrame
    df = pd.DataFrame(rows_data, columns=headers)
    
    # Filter to keep only Activity ID, Latitude, and Longitude columns
    columns_to_keep = ['Activity ID', 'Latitude', 'Longitude']
    
    # Check if columns exist (handle potential column name variations)
    available_columns = []
    for col in columns_to_keep:
        # Try exact match first
        if col in df.columns:
            available_columns.append(col)
        else:
            # Try to find similar column names (case-insensitive, with/without spaces)
            for df_col in df.columns:
                if col.lower().replace(' ', '') in df_col.lower().replace(' ', ''):
                    available_columns.append(df_col)
                    break
    
    if not available_columns:
        print("Required columns not found in the data")
        print("Available columns:", df.columns.tolist())
        return pd.DataFrame()
    
    # Filter DataFrame to keep only the required columns
    filtered_df = df[available_columns].copy()
    
    # Clean up latitude and longitude columns (remove extra spaces, convert to numeric)
    for col in filtered_df.columns:
        if 'latitude' in col.lower():
            filtered_df[col] = pd.to_numeric(filtered_df[col].astype(str).str.strip(), errors='coerce')
        elif 'longitude' in col.lower():
            filtered_df[col] = pd.to_numeric(filtered_df[col].astype(str).str.strip(), errors='coerce')
    # Rename Activity ID column to Activity_ID
    column_mapping = {}
    for col in filtered_df.columns:
        if 'activity' in col.lower() and 'id' in col.lower():
            column_mapping[col] = 'Activity_ID'

    if column_mapping:
        filtered_df = filtered_df.rename(columns=column_mapping)
    return filtered_df

def get_filtered_surveillance_data(cookie_value, target_date, town_code, uc_code):
    """
    Complete function to fetch and parse surveillance data.
    Returns DataFrame with Activity ID, Latitude, and Longitude only.
    """
    # Fetch raw HTML data
    html_content = get_excel_data(cookie_value, target_date, town_code, uc_code)
    
    if html_content is None:
        return pd.DataFrame()
    
    # Parse HTML and extract required columns
    df = parse_html_table_to_dataframe(html_content)
    
    return df

def get_scrapped_data(cookie_value, target_date, town_data, uc_data):
    """
    Fetches and parses surveillance data for all towns and UCs.
    Returns two DataFrames:
    1. Main data without Container Tag, Checked, and Positive columns
    2. Container data with Activity ID, Container Tag, Checked, and Positive
    """
    all_main_data = []
    all_container_data = []
    
    # First, get a sample page to calculate total pages
    sample_page_data = fetch_page_data(cookie_value, pd.to_datetime("2025-06-23"), 149, 2917, 1)
    total_records = get_total_records(sample_page_data)
    total_pages = total_records // 20 + 1
    print(f"Total pages to process: {total_pages}")
    
    # Loop through all pages
    for page_number in range(1, total_pages + 1):
        print(f"Processing page {page_number}/{total_pages}")
        
        # Fetch page data
        page_data = fetch_page_data(cookie_value, pd.to_datetime("2025-06-23"), 149, 2917, page_number)
        
        # Parse the HTML table
        main_df, container_df = parse_html_table(page_data)
        
        if not main_df.empty:
            all_main_data.append(main_df)
        if not container_df.empty:
            all_container_data.append(container_df)
    
    # Concatenate all data
    if all_main_data:
        final_main_df = pd.concat(all_main_data, ignore_index=True)
    else:
        final_main_df = pd.DataFrame()
    
    if all_container_data:
        final_container_df = pd.concat(all_container_data, ignore_index=True)
    else:
        final_container_df = pd.DataFrame()
    
    print(f"Total main records: {len(final_main_df)}")
    print(f"Total container records: {len(final_container_df)}")
    
    return final_main_df, final_container_df



def parse_html_table(html_content):
    """
    Parses HTML table and returns two DataFrames:
    1. Main data without Container Tag, Checked, and Positive
    2. Container data with Activity ID, Container Tag, Checked, and Positive
    """
    soup = BeautifulSoup(html_content, 'html.parser')
    table = soup.find('table', {'id': 'p_table'})
    
    if not table:
        return pd.DataFrame(), pd.DataFrame()
    
    main_data = []
    container_data = []
    
    # Find all rows in tbody
    tbody = table.find('tbody')
    if not tbody:
        return pd.DataFrame(), pd.DataFrame()
    
    rows = tbody.find_all('tr')
    
    for row in rows:
        cells = row.find_all('td')
        if len(cells) < 16:  # Skip if not enough columns
            continue
        
        # Extract main data (excluding Container Tag, Checked, Positive)
        main_record = {
            'Sr_No': cells[0].get_text(strip=True),
            'Activity_ID': cells[1].get_text(strip=True),
            'Name_of_Family_Head': cells[2].get_text(strip=True),
            'Shop_House': cells[3].get_text(strip=True),
            'Address': cells[4].get_text(strip=True),
            'Locality': cells[5].get_text(strip=True),
            'District': cells[7].get_text(strip=True),
            'Town': cells[8].get_text(strip=True),
            'UC': cells[9].get_text(strip=True),
            'Tag': cells[10].get_text(strip=True),
            'Submitted_by': cells[13].get_text(strip=True),
            'Activity_DateTime': cells[14].get_text(strip=True),
            'Picture': cells[15].get_text(strip=True)
        }
        main_data.append(main_record)
        
        # Extract container data
        activity_id = cells[1].get_text(strip=True)
        
        # Parse Container Tags (column 6)
        container_tags = []
        container_cell = cells[6]
        container_paragraphs = container_cell.find_all('p')
        for p in container_paragraphs:
            tag_text = p.get_text(strip=True)
            if tag_text:
                container_tags.append(tag_text)
        
        # Parse Checked values (column 11)
        checked_values = []
        checked_cell = cells[11]
        checked_paragraphs = checked_cell.find_all('p')
        for p in checked_paragraphs:
            checked_text = p.get_text(strip=True)
            if checked_text:
                checked_values.append(checked_text)
        
        # Parse Positive values (column 12)
        positive_values = []
        positive_cell = cells[12]
        positive_paragraphs = positive_cell.find_all('p')
        for p in positive_paragraphs:
            positive_text = p.get_text(strip=True)
            if positive_text:
                positive_values.append(positive_text)
        
        # Create container records (one for each container tag)
        max_length = max(len(container_tags), len(checked_values), len(positive_values))
        
        for i in range(max_length):
            container_record = {
                'Activity_ID': activity_id,
                'Container_Tag': container_tags[i] if i < len(container_tags) else '',
                'Checked': checked_values[i] if i < len(checked_values) else '',
                'Positive': positive_values[i] if i < len(positive_values) else ''
            }
            container_data.append(container_record)
    
    # Create DataFrames
    main_df = pd.DataFrame(main_data)
    container_df = pd.DataFrame(container_data)
    
    return main_df, container_df

# Helper function to clean and convert data types if needed
def clean_dataframes(main_df, container_df):
    """
    Clean and convert data types for the DataFrames
    """
    if not main_df.empty:
        # Convert Activity_ID to string to ensure consistency
        main_df['Activity_ID'] = main_df['Activity_ID'].astype(str)
        
        # Clean datetime field
        main_df['Activity_DateTime'] = main_df['Activity_DateTime'].str.replace('on ', '').str.replace(' at ', ' ')
    
    if not container_df.empty:
        # Convert Activity_ID to string
        container_df['Activity_ID'] = container_df['Activity_ID'].astype(str)
        
        # Convert Checked and Positive to numeric
        container_df['Checked'] = pd.to_numeric(container_df['Checked'], errors='coerce').fillna(0).astype(int)
        container_df['Positive'] = pd.to_numeric(container_df['Positive'], errors='coerce').fillna(0).astype(int)
    
    return main_df, container_df

# Updated main function with data cleaning
def get_scrapped_data_cleaned(cookie_value, target_date, town_data, uc_data):
    """
    Main function that returns cleaned DataFrames
    """
    main_df, container_df = get_scrapped_data(cookie_value, target_date, town_data, uc_data)
    return clean_dataframes(main_df, container_df)

def combine_data(cookie_value, target_date, town_data, uc_data):
    main_df, container_df = get_scrapped_data_cleaned(cookie_value, target_date, town_data, uc_data)
    locations_df = get_filtered_surveillance_data(cookie_value, target_date, town_data, uc_data)
    combined_df = main_df.merge(locations_df, on='Activity_ID', how='left')
    return combined_df, container_df

combined_df, container_df = combine_data(cookie_value, pd.to_datetime("2025-06-23"), 149, 2917)

combined_df.head(3)