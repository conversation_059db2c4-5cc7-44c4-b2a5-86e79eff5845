{"cells": [{"cell_type": "code", "execution_count": 1, "id": "2661ca7e", "metadata": {}, "outputs": [], "source": ["from dotenv import load_dotenv\n", "import os\n", "import re\n", "import requests\n", "import json\n", "from bs4 import BeautifulSoup\n", "# Load environment variables\n", "load_dotenv()\n", "import pandas as pd\n"]}, {"cell_type": "code", "execution_count": 2, "id": "a9cc5716", "metadata": {}, "outputs": [], "source": ["PITB_USERNAME = os.getenv(\"PITB_USERNAME\")\n", "PITB_PASSWORD = os.getenv(\"PITB_PASSWORD\")\n", "REPORT_TYPE = \"indoor\"\n", "COOKIE_URL = os.getenv(\"COOKIE_URL\")"]}, {"cell_type": "code", "execution_count": 3, "id": "2ee7a262", "metadata": {}, "outputs": [], "source": ["def get_cookie_value():\n", "    \"\"\"\n", "    Retrieve authentication cookie from the cookie service.\n", "    Returns a cookie value for dashboard authentication.\n", "    \"\"\"\n", "    url = COOKIE_URL\n", "\n", "    # Prepare authentication payload\n", "    payload = json.dumps({\"username\": PITB_USERNAME, \"password\": PITB_PASSWORD})\n", "\n", "    # Set content type for JSON request\n", "    headers = {\"Content-Type\": \"application/json\"}\n", "\n", "    # Make authentication request\n", "    response = requests.post(url, headers=headers, data=payload)\n", "    response.raise_for_status()  # Raise exception for HTTP errors\n", "\n", "    # Parse response and extract cookie\n", "    response_json = response.json()\n", "    print(response_json)\n", "    if response_json.get(\"success\") and \"cookies\" in response_json:\n", "        return response_json[\"cookies\"][0][\"value\"]\n", "    else:\n", "        raise ValueError(\"Invalid response format or unsuccessful request.\")\n"]}, {"cell_type": "code", "execution_count": 4, "id": "16fd2fa8", "metadata": {}, "outputs": [], "source": ["def fetch_page_data(cookie_value, target_date, town_code, uc_code, page_number=1):\n", "    \"\"\"\n", "    Fetch page data from the dashboard API.\n", "    Returns a JSON object containing page data.\n", "    \"\"\"\n", "    formatted_date = target_date.strftime(\"%Y-%m-%d\")\n", "\n", "    headers = {\"Cookie\": f\"_dengue_new_session={cookie_value}\"}\n", "    \n", "    url = f\"https://dashboard-tracking.punjab.gov.pk/activities/vector_surveillances/line_list?activity_type=indoor&datefrom={formatted_date}T00%3A00&dateto={formatted_date}T23%3A59&district_id=31&larvae_found=&page={page_number}&tehsil_id={town_code}&uc={uc_code}\"\n", "\n", "    response = requests.get(url, headers=headers, data={}, verify=False)\n", "\n", "    # Debug information\n", "    # print(f\"Response URL: {response.url}\")\n", "    # print(f\"Response status code: {response.status_code}\")\n", "\n", "    # Check if we got redirected to login page\n", "    if \"login\" in response.url.lower() or \"sign in\" in response.text.lower():\n", "        print(\"Authentication failed - redirected to login page\")\n", "\n", "    response.raise_for_status()\n", "    return response.content\n"]}, {"cell_type": "code", "execution_count": 5, "id": "22a3172d", "metadata": {}, "outputs": [], "source": ["def read_town_uc_codes(file_path):\n", "  \"\"\"\n", "  Reads the 'town-uc-codes.xlsx' file and returns:\n", "  - town_data: DataFrame with columns ['town_name', 'town_code'] from the 'town' sheet\n", "  - uc_data: DataFrame with columns ['uc_name', 'town_code', 'uc_code'] from the 'uc' sheet\n", "  \"\"\"\n", "\n", "  # Read the 'town' sheet\n", "  town_data = pd.read_excel(file_path, sheet_name='town', usecols=['town_name', 'town_code'])\n", "\n", "  # Read the 'uc' sheet\n", "  uc_data = pd.read_excel(file_path, sheet_name='uc', usecols=['uc_name', 'town_code', 'uc_code'])\n", "\n", "  return town_data, uc_data"]}, {"cell_type": "code", "execution_count": 6, "id": "722ccf2b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'success': True, 'cookies': [{'name': '_dengue_new_session', 'value': '1CQqMIhQ0ESjTG%2FowO737X85V9xEhWQTdsbXjlpLu%2Bho3qSoRlRJwf4qIcsKbYBFJCvF53uDtsfFbiRI8eziR0N9QWKLp68LI8wftq74eBd07%2FEZnVCKIMADqkJGNaOm3TeKBd29slheBR%2FYc%2FhjbyTnOzrJMOP0jaxID5jhBUvCwXDJrDhhmiOexYVdSrPJcmcNLQizDD1rvrgaR0Pn5IY9x9yWHkr%2FJ%2BpKUsU%2FmraZ5OK3SEN%2BQA0Yyr17cvsQIg%2FawrZdpBwA4%2FDz%2BORruDxtYk6es%2FgaaKZmgcbG48c4dD1USabHRgtrXh73IXXubzbNqmJ5t93oHF5aja8SRt%2BZIgWAD0ijvu27%2BRxvWH0YzjIc0VbsY5ic%2FY6cKPOjNTbrL8tx%2BBqQ%2F%2BIcIppnOhoqmN1ThlmLwVqUWCCqUmLB%2FfewU2MDVbh0dPtZXn2uZqXK92NGGmhvv0ndWTtwA%2ByZUuzpUvKTFF5tYIJxehWIAE8OoyqUkEHn4I9BTiAIGJG7Oy9F7w4wTEcsK2puYVxdXrI1ZvQ%3D--x%2FU%2Bs92wN9d%2BC5fB--RBHFNMH4RWn4ccqoUzd0cg%3D%3D', 'domain': 'dashboard-tracking.punjab.gov.pk', 'path': '/', 'expires': -1, 'size': 661, 'httpOnly': True, 'secure': False, 'session': True, 'priority': 'Medium', 'sameParty': False, 'sourceScheme': 'Secure'}]}\n"]}], "source": ["cookie_value = get_cookie_value()"]}, {"cell_type": "code", "execution_count": 7, "id": "7ebf70d8", "metadata": {}, "outputs": [], "source": ["def get_total_records(page_data):\n", "  # Extract the number after \"Total Records:\"\n", "  match = re.search(r'Total Records:\\s*</b>\\s*(\\d+)', page_data.decode('utf-8'))\n", "  if match:\n", "    total_records = int(match.group(1))\n", "    print(total_records)\n", "  else:\n", "    print(\"Total Records not found.\")\n", "  return total_records"]}, {"cell_type": "code", "execution_count": 8, "id": "6f0e0161", "metadata": {}, "outputs": [], "source": ["def get_excel_data(cookie_value, target_date, town_code, uc_code, page_number=1):\n", "    \"\"\"\n", "    Fetch page data from the dashboard API.\n", "    Returns a JSON object containing page data.\n", "    \"\"\"\n", "    formatted_date = target_date.strftime(\"%Y-%m-%d\")\n", "\n", "    headers = {\"Cookie\": f\"_dengue_new_session={cookie_value}\"}\n", "    \n", "    url = f\"https://dashboard-tracking.punjab.gov.pk//activities/vector_surveillances/line_list?action=line_list&activity_type=indoor&controller=activities%2Fvector_surveillances&datefrom={formatted_date}T00%3A00&dateto={formatted_date}T23%3A59&district_id=31&format=xls&larvae_found=&page=1&pagination=No&tehsil_id={town_code}&uc={uc_code}\"\n", "\n", "    response = requests.get(url, headers=headers, data={}, verify=False)\n", "\n", "    # Debug information\n", "    # print(f\"Response URL: {response.url}\")\n", "    # print(f\"Response status code: {response.status_code}\")\n", "\n", "    # Check if we got redirected to login page\n", "    if \"login\" in response.url.lower() or \"sign in\" in response.text.lower():\n", "        print(\"Authentication failed - redirected to login page\")\n", "\n", "    response.raise_for_status()\n", "    return response.content\n"]}, {"cell_type": "code", "execution_count": 9, "id": "6a412973", "metadata": {}, "outputs": [], "source": ["def parse_html_table_to_dataframe(html_content):\n", "    \"\"\"\n", "    Parse HTML table content and extract Activity ID, Latitude, and Longitude columns.\n", "    Returns a pandas DataFrame with only the specified columns.\n", "    \"\"\"\n", "    # Parse HTML content\n", "    soup = BeautifulSoup(html_content, 'html.parser')\n", "    \n", "    # Find the table\n", "    table = soup.find('table')\n", "    if not table:\n", "        print(\"No table found in HTML content\")\n", "        return pd.DataFrame()\n", "    \n", "    # Extract headers\n", "    headers = []\n", "    header_row = table.find('thead').find('tr')\n", "    for th in header_row.find_all('th'):\n", "        headers.append(th.get_text(strip=True))\n", "    \n", "    # Extract data rows\n", "    rows_data = []\n", "    tbody = table.find('tbody')\n", "    if tbody:\n", "        for row in tbody.find_all('tr'):\n", "            row_data = []\n", "            for td in row.find_all('td'):\n", "                # Get text content, replacing <br/> with spaces if needed\n", "                cell_text = td.get_text(separator=' ', strip=True)\n", "                row_data.append(cell_text)\n", "            rows_data.append(row_data)\n", "    \n", "    # Create DataFrame\n", "    df = pd.DataFrame(rows_data, columns=headers)\n", "    \n", "    # Filter to keep only Activity ID, Latitude, and Longitude columns\n", "    columns_to_keep = ['Activity ID', 'Latitude', 'Longitude']\n", "    \n", "    # Check if columns exist (handle potential column name variations)\n", "    available_columns = []\n", "    for col in columns_to_keep:\n", "        # Try exact match first\n", "        if col in df.columns:\n", "            available_columns.append(col)\n", "        else:\n", "            # Try to find similar column names (case-insensitive, with/without spaces)\n", "            for df_col in df.columns:\n", "                if col.lower().replace(' ', '') in df_col.lower().replace(' ', ''):\n", "                    available_columns.append(df_col)\n", "                    break\n", "    \n", "    if not available_columns:\n", "        print(\"Required columns not found in the data\")\n", "        print(\"Available columns:\", df.columns.tolist())\n", "        return pd.DataFrame()\n", "    \n", "    # Filter DataFrame to keep only the required columns\n", "    filtered_df = df[available_columns].copy()\n", "    \n", "    # Clean up latitude and longitude columns (remove extra spaces, convert to numeric)\n", "    for col in filtered_df.columns:\n", "        if 'latitude' in col.lower():\n", "            filtered_df[col] = pd.to_numeric(filtered_df[col].astype(str).str.strip(), errors='coerce')\n", "        elif 'longitude' in col.lower():\n", "            filtered_df[col] = pd.to_numeric(filtered_df[col].astype(str).str.strip(), errors='coerce')\n", "    # Rename Activity ID column to Activity_ID\n", "    column_mapping = {}\n", "    for col in filtered_df.columns:\n", "        if 'activity' in col.lower() and 'id' in col.lower():\n", "            column_mapping[col] = 'Activity_ID'\n", "\n", "    if column_mapping:\n", "        filtered_df = filtered_df.rename(columns=column_mapping)\n", "    return filtered_df"]}, {"cell_type": "code", "execution_count": 10, "id": "38dca1d6", "metadata": {}, "outputs": [], "source": ["def get_filtered_surveillance_data(cookie_value, target_date, town_code, uc_code):\n", "    \"\"\"\n", "    Complete function to fetch and parse surveillance data.\n", "    Returns DataFrame with Activity ID, Latitude, and Longitude only.\n", "    \"\"\"\n", "    # Fetch raw HTML data\n", "    html_content = get_excel_data(cookie_value, target_date, town_code, uc_code)\n", "    \n", "    if html_content is None:\n", "        return pd.DataFrame()\n", "    \n", "    # Parse HTML and extract required columns\n", "    df = parse_html_table_to_dataframe(html_content)\n", "    \n", "    return df"]}, {"cell_type": "code", "execution_count": 11, "id": "77aa0d25", "metadata": {}, "outputs": [], "source": ["def get_scrapped_data(cookie_value, target_date, town_data, uc_data):\n", "    \"\"\"\n", "    Fetches and parses surveillance data for all towns and UCs.\n", "    Returns two DataFrames:\n", "    1. Main data without Container Tag, Checked, and Positive columns\n", "    2. Container data with Activity ID, Container Tag, Checked, and Positive\n", "    \"\"\"\n", "    all_main_data = []\n", "    all_container_data = []\n", "    \n", "    # First, get a sample page to calculate total pages\n", "    sample_page_data = fetch_page_data(cookie_value, pd.to_datetime(\"2025-06-23\"), 149, 2917, 1)\n", "    total_records = get_total_records(sample_page_data)\n", "    total_pages = total_records // 20 + 1\n", "    print(f\"Total pages to process: {total_pages}\")\n", "    \n", "    # Loop through all pages\n", "    for page_number in range(1, total_pages + 1):\n", "        print(f\"Processing page {page_number}/{total_pages}\")\n", "        \n", "        # Fetch page data\n", "        page_data = fetch_page_data(cookie_value, pd.to_datetime(\"2025-06-23\"), 149, 2917, page_number)\n", "        \n", "        # Parse the HTML table\n", "        main_df, container_df = parse_html_table(page_data)\n", "        \n", "        if not main_df.empty:\n", "            all_main_data.append(main_df)\n", "        if not container_df.empty:\n", "            all_container_data.append(container_df)\n", "    \n", "    # Concatenate all data\n", "    if all_main_data:\n", "        final_main_df = pd.concat(all_main_data, ignore_index=True)\n", "    else:\n", "        final_main_df = pd.DataFrame()\n", "    \n", "    if all_container_data:\n", "        final_container_df = pd.concat(all_container_data, ignore_index=True)\n", "    else:\n", "        final_container_df = pd.DataFrame()\n", "    \n", "    print(f\"Total main records: {len(final_main_df)}\")\n", "    print(f\"Total container records: {len(final_container_df)}\")\n", "    \n", "    return final_main_df, final_container_df\n", "\n"]}, {"cell_type": "code", "execution_count": 12, "id": "892c3b64", "metadata": {}, "outputs": [], "source": ["def parse_html_table(html_content):\n", "    \"\"\"\n", "    Parses HTML table and returns two DataFrames:\n", "    1. Main data without Container Tag, Checked, and Positive\n", "    2. Container data with Activity ID, Container Tag, Checked, and Positive\n", "    \"\"\"\n", "    soup = BeautifulSoup(html_content, 'html.parser')\n", "    table = soup.find('table', {'id': 'p_table'})\n", "    \n", "    if not table:\n", "        return pd.DataFrame(), pd.DataFrame()\n", "    \n", "    main_data = []\n", "    container_data = []\n", "    \n", "    # Find all rows in tbody\n", "    tbody = table.find('tbody')\n", "    if not tbody:\n", "        return pd.DataFrame(), pd.DataFrame()\n", "    \n", "    rows = tbody.find_all('tr')\n", "    \n", "    for row in rows:\n", "        cells = row.find_all('td')\n", "        if len(cells) < 16:  # Skip if not enough columns\n", "            continue\n", "        \n", "        # Extract main data (excluding Container Tag, Checked, Positive)\n", "        main_record = {\n", "            'Sr_No': cells[0].get_text(strip=True),\n", "            'Activity_ID': cells[1].get_text(strip=True),\n", "            'Name_of_Family_Head': cells[2].get_text(strip=True),\n", "            'Shop_House': cells[3].get_text(strip=True),\n", "            'Address': cells[4].get_text(strip=True),\n", "            'Locality': cells[5].get_text(strip=True),\n", "            'District': cells[7].get_text(strip=True),\n", "            'Town': cells[8].get_text(strip=True),\n", "            'UC': cells[9].get_text(strip=True),\n", "            'Tag': cells[10].get_text(strip=True),\n", "            'Submitted_by': cells[13].get_text(strip=True),\n", "            'Activity_DateTime': cells[14].get_text(strip=True),\n", "            'Picture': cells[15].get_text(strip=True)\n", "        }\n", "        main_data.append(main_record)\n", "        \n", "        # Extract container data\n", "        activity_id = cells[1].get_text(strip=True)\n", "        \n", "        # Parse Container Tags (column 6)\n", "        container_tags = []\n", "        container_cell = cells[6]\n", "        container_paragraphs = container_cell.find_all('p')\n", "        for p in container_paragraphs:\n", "            tag_text = p.get_text(strip=True)\n", "            if tag_text:\n", "                container_tags.append(tag_text)\n", "        \n", "        # Parse Checked values (column 11)\n", "        checked_values = []\n", "        checked_cell = cells[11]\n", "        checked_paragraphs = checked_cell.find_all('p')\n", "        for p in checked_paragraphs:\n", "            checked_text = p.get_text(strip=True)\n", "            if checked_text:\n", "                checked_values.append(checked_text)\n", "        \n", "        # Parse Positive values (column 12)\n", "        positive_values = []\n", "        positive_cell = cells[12]\n", "        positive_paragraphs = positive_cell.find_all('p')\n", "        for p in positive_paragraphs:\n", "            positive_text = p.get_text(strip=True)\n", "            if positive_text:\n", "                positive_values.append(positive_text)\n", "        \n", "        # Create container records (one for each container tag)\n", "        max_length = max(len(container_tags), len(checked_values), len(positive_values))\n", "        \n", "        for i in range(max_length):\n", "            container_record = {\n", "                'Activity_ID': activity_id,\n", "                'Container_Tag': container_tags[i] if i < len(container_tags) else '',\n", "                'Checked': checked_values[i] if i < len(checked_values) else '',\n", "                'Positive': positive_values[i] if i < len(positive_values) else ''\n", "            }\n", "            container_data.append(container_record)\n", "    \n", "    # Create DataFrames\n", "    main_df = pd.DataFrame(main_data)\n", "    container_df = pd.DataFrame(container_data)\n", "    \n", "    return main_df, container_df"]}, {"cell_type": "code", "execution_count": 13, "id": "f1f5540e", "metadata": {}, "outputs": [], "source": ["# Helper function to clean and convert data types if needed\n", "def clean_dataframes(main_df, container_df):\n", "    \"\"\"\n", "    Clean and convert data types for the DataFrames\n", "    \"\"\"\n", "    if not main_df.empty:\n", "        # Convert Activity_ID to string to ensure consistency\n", "        main_df['Activity_ID'] = main_df['Activity_ID'].astype(str)\n", "        \n", "        # Clean datetime field\n", "        main_df['Activity_DateTime'] = main_df['Activity_DateTime'].str.replace('on ', '').str.replace(' at ', ' ')\n", "    \n", "    if not container_df.empty:\n", "        # Convert Activity_ID to string\n", "        container_df['Activity_ID'] = container_df['Activity_ID'].astype(str)\n", "        \n", "        # Convert Checked and Positive to numeric\n", "        container_df['Checked'] = pd.to_numeric(container_df['Checked'], errors='coerce').fillna(0).astype(int)\n", "        container_df['Positive'] = pd.to_numeric(container_df['Positive'], errors='coerce').fillna(0).astype(int)\n", "    \n", "    return main_df, container_df"]}, {"cell_type": "code", "execution_count": 14, "id": "a2bcc125", "metadata": {}, "outputs": [], "source": ["# Updated main function with data cleaning\n", "def get_scrapped_data_cleaned(cookie_value, target_date, town_data, uc_data):\n", "    \"\"\"\n", "    Main function that returns cleaned DataFrames\n", "    \"\"\"\n", "    main_df, container_df = get_scrapped_data(cookie_value, target_date, town_data, uc_data)\n", "    return clean_dataframes(main_df, container_df)"]}, {"cell_type": "code", "execution_count": 15, "id": "04eb59ab", "metadata": {}, "outputs": [], "source": ["def combine_data(cookie_value, target_date, town_data, uc_data):\n", "    main_df, container_df = get_scrapped_data_cleaned(cookie_value, target_date, town_data, uc_data)\n", "    locations_df = get_filtered_surveillance_data(cookie_value, target_date, town_data, uc_data)\n", "    combined_df = main_df.merge(locations_df, on='Activity_ID', how='left')\n", "    return combined_df, container_df"]}, {"cell_type": "code", "execution_count": 16, "id": "49163c51", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\Documents\\genai\\dengue\\scrapers\\dts-script-maker\\.venv\\Lib\\site-packages\\urllib3\\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'dashboard-tracking.punjab.gov.pk'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["76\n", "Total pages to process: 4\n", "Processing page 1/4\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\Documents\\genai\\dengue\\scrapers\\dts-script-maker\\.venv\\Lib\\site-packages\\urllib3\\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'dashboard-tracking.punjab.gov.pk'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processing page 2/4\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\Documents\\genai\\dengue\\scrapers\\dts-script-maker\\.venv\\Lib\\site-packages\\urllib3\\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'dashboard-tracking.punjab.gov.pk'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processing page 3/4\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\Documents\\genai\\dengue\\scrapers\\dts-script-maker\\.venv\\Lib\\site-packages\\urllib3\\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'dashboard-tracking.punjab.gov.pk'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processing page 4/4\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\Documents\\genai\\dengue\\scrapers\\dts-script-maker\\.venv\\Lib\\site-packages\\urllib3\\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'dashboard-tracking.punjab.gov.pk'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Total main records: 76\n", "Total container records: 497\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\Documents\\genai\\dengue\\scrapers\\dts-script-maker\\.venv\\Lib\\site-packages\\urllib3\\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'dashboard-tracking.punjab.gov.pk'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}], "source": ["combined_df, container_df = combine_data(cookie_value, pd.to_datetime(\"2025-06-23\"), 149, 2917)"]}, {"cell_type": "code", "execution_count": 17, "id": "9363c0a8", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Sr_No</th>\n", "      <th>Activity_ID</th>\n", "      <th>Name_of_Family_Head</th>\n", "      <th>Shop_House</th>\n", "      <th>Address</th>\n", "      <th>Locality</th>\n", "      <th>District</th>\n", "      <th>Town</th>\n", "      <th>UC</th>\n", "      <th>Tag</th>\n", "      <th>Submitted_by</th>\n", "      <th>Activity_DateTime</th>\n", "      <th>Picture</th>\n", "      <th>Latitude</th>\n", "      <th>Longitude</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>20709897</td>\n", "      <td>zu<PERSON><PERSON></td>\n", "      <td>2</td>\n", "      <td>11</td>\n", "      <td>tamasamabad</td>\n", "      <td>Rawalpindi</td>\n", "      <td>Chaklala Cantonment</td>\n", "      <td>CTC-1</td>\n", "      <td>indoor</td>\n", "      <td>3740430762382</td>\n", "      <td>06/23/2025 01:15PM</td>\n", "      <td>Before</td>\n", "      <td>33.605197</td>\n", "      <td>73.079270</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>20709894</td>\n", "      <td>zaheer</td>\n", "      <td>3</td>\n", "      <td>11</td>\n", "      <td>tamasamabad</td>\n", "      <td>Rawalpindi</td>\n", "      <td>Chaklala Cantonment</td>\n", "      <td>CTC-1</td>\n", "      <td>indoor</td>\n", "      <td>3740430762382</td>\n", "      <td>06/23/2025 01:15PM</td>\n", "      <td>Before</td>\n", "      <td>33.605218</td>\n", "      <td>73.079330</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>20709888</td>\n", "      <td>asad</td>\n", "      <td>4</td>\n", "      <td>11</td>\n", "      <td>tamasamabad</td>\n", "      <td>Rawalpindi</td>\n", "      <td>Chaklala Cantonment</td>\n", "      <td>CTC-1</td>\n", "      <td>indoor</td>\n", "      <td>3740430762382</td>\n", "      <td>06/23/2025 01:15PM</td>\n", "      <td>Before</td>\n", "      <td>33.605262</td>\n", "      <td>73.079348</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Sr_No Activity_ID Name_of_Family_Head Shop_House Address     Locality  \\\n", "0     1    20709897              zubair          2      11  tamasamabad   \n", "1     2    20709894              zah<PERSON>          3      11  tamasamabad   \n", "2     3    20709888                asad          4      11  tamasamabad   \n", "\n", "     District                 Town     UC     Tag   Submitted_by  \\\n", "0  Rawalpindi  Chaklala Cantonment  CTC-1  indoor  3740430762382   \n", "1  Rawalpindi  Chaklala Cantonment  CTC-1  indoor  3740430762382   \n", "2  Rawalpindi  Chaklala Cantonment  CTC-1  indoor  3740430762382   \n", "\n", "    Activity_DateTime Picture   Latitude  Longitude  \n", "0  06/23/2025 01:15PM  Before  33.605197  73.079270  \n", "1  06/23/2025 01:15PM  Before  33.605218  73.079330  \n", "2  06/23/2025 01:15PM  Before  33.605262  73.079348  "]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["combined_df.head(3)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 5}